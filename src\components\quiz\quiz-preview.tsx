'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Save, Edit, Trash2, Plus, Eye, EyeOff } from 'lucide-react';

interface QuizPreviewProps {
  config: any;
  questions: any[];
  onSave: () => void;
  onBack: () => void;
  onUpdateQuestions: (questions: any[]) => void;
  isSaving: boolean;
}

export function QuizPreview({
  config,
  questions,
  onSave,
  onBack,
  onUpdateQuestions,
  isSaving,
}: QuizPreviewProps) {
  const [editingQuestion, setEditingQuestion] = useState<number | null>(null);
  const [showAnswers, setShowAnswers] = useState(false);

  const handleEditQuestion = (index: number, updatedQuestion: any) => {
    const newQuestions = [...questions];
    newQuestions[index] = updatedQuestion;
    onUpdateQuestions(newQuestions);
    setEditingQuestion(null);
  };

  const handleDeleteQuestion = (index: number) => {
    const newQuestions = questions.filter((_, i) => i !== index);
    onUpdateQuestions(newQuestions);
  };

  const handleAddQuestion = () => {
    const newQuestion = {
      type: 'MULTIPLE_CHOICE',
      question: 'New question',
      options: ['Option A', 'Option B', 'Option C', 'Option D'],
      correctAnswer: 'Option A',
      explanation: '',
      difficulty: config.difficulty,
      points: 1,
    };
    onUpdateQuestions([...questions, newQuestion]);
    setEditingQuestion(questions.length);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            Preview & Edit Quiz
          </h2>
          <p className="text-gray-600">
            Review and customize your AI-generated questions
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAnswers(!showAnswers)}
          >
            {showAnswers ? (
              <>
                <EyeOff className="mr-2 h-4 w-4" />
                Hide Answers
              </>
            ) : (
              <>
                <Eye className="mr-2 h-4 w-4" />
                Show Answers
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Quiz Info */}
      <Card className="bg-gray-50">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Title:</span>
              <p className="text-gray-900">{config.title}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Questions:</span>
              <p className="text-gray-900">{questions.length}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Difficulty:</span>
              <p className="text-gray-900">{config.difficulty}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Visibility:</span>
              <p className="text-gray-900">{config.isPublic ? 'Public' : 'Private'}</p>
            </div>
          </div>
          {config.description && (
            <div className="mt-3 pt-3 border-t">
              <span className="font-medium text-gray-700">Description:</span>
              <p className="text-gray-900 mt-1">{config.description}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Questions */}
      <div className="space-y-4">
        {questions.map((question, index) => (
          <QuestionCard
            key={index}
            question={question}
            index={index}
            isEditing={editingQuestion === index}
            showAnswer={showAnswers}
            onEdit={() => setEditingQuestion(index)}
            onSave={(updatedQuestion) => handleEditQuestion(index, updatedQuestion)}
            onCancel={() => setEditingQuestion(null)}
            onDelete={() => handleDeleteQuestion(index)}
          />
        ))}

        <Card className="border-dashed border-2 border-gray-300">
          <CardContent className="p-6 text-center">
            <Button variant="outline" onClick={handleAddQuestion}>
              <Plus className="mr-2 h-4 w-4" />
              Add Question
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Configuration
        </Button>
        <Button onClick={onSave} disabled={isSaving || questions.length === 0}>
          {isSaving ? (
            <>
              <Save className="mr-2 h-4 w-4 animate-pulse" />
              Saving Quiz...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Quiz
            </>
          )}
        </Button>
      </div>
    </div>
  );
}

function QuestionCard({
  question,
  index,
  isEditing,
  showAnswer,
  onEdit,
  onSave,
  onCancel,
  onDelete,
}: {
  question: any;
  index: number;
  isEditing: boolean;
  showAnswer: boolean;
  onEdit: () => void;
  onSave: (question: any) => void;
  onCancel: () => void;
  onDelete: () => void;
}) {
  const [editedQuestion, setEditedQuestion] = useState(question);

  if (isEditing) {
    return (
      <Card className="border-blue-500">
        <CardHeader>
          <CardTitle className="text-lg">Edit Question {index + 1}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Question
            </label>
            <textarea
              value={editedQuestion.question}
              onChange={(e) => setEditedQuestion(prev => ({ ...prev, question: e.target.value }))}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={3}
            />
          </div>

          {(editedQuestion.type === 'MULTIPLE_CHOICE' || editedQuestion.type === 'TRUE_FALSE') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Options
              </label>
              <div className="space-y-2">
                {editedQuestion.options.map((option: string, optionIndex: number) => (
                  <div key={optionIndex} className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name={`correct-${index}`}
                      checked={editedQuestion.correctAnswer === option}
                      onChange={() => setEditedQuestion(prev => ({ ...prev, correctAnswer: option }))}
                      className="text-blue-600 focus:ring-blue-500"
                    />
                    <input
                      type="text"
                      value={option}
                      onChange={(e) => {
                        const newOptions = [...editedQuestion.options];
                        newOptions[optionIndex] = e.target.value;
                        setEditedQuestion(prev => ({ ...prev, options: newOptions }));
                        if (editedQuestion.correctAnswer === option) {
                          setEditedQuestion(prev => ({ ...prev, correctAnswer: e.target.value }));
                        }
                      }}
                      className="flex-1 p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          {editedQuestion.type === 'FILL_IN_BLANK' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Correct Answer
              </label>
              <input
                type="text"
                value={editedQuestion.correctAnswer}
                onChange={(e) => setEditedQuestion(prev => ({ ...prev, correctAnswer: e.target.value }))}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Explanation (Optional)
            </label>
            <textarea
              value={editedQuestion.explanation}
              onChange={(e) => setEditedQuestion(prev => ({ ...prev, explanation: e.target.value }))}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={2}
            />
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button onClick={() => onSave(editedQuestion)}>
              Save Changes
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-start justify-between">
        <div className="flex-1">
          <CardTitle className="text-lg">
            Question {index + 1}
            <span className="ml-2 text-sm font-normal text-gray-600">
              ({question.type.replace('_', ' ')})
            </span>
          </CardTitle>
          <p className="text-gray-700 mt-2">{question.question}</p>
        </div>
        <div className="flex space-x-1">
          <Button variant="ghost" size="sm" onClick={onEdit}>
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={onDelete}>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {(question.type === 'MULTIPLE_CHOICE' || question.type === 'TRUE_FALSE') && (
          <div className="space-y-2">
            {question.options.map((option: string, optionIndex: number) => (
              <div
                key={optionIndex}
                className={`p-3 rounded-lg border ${
                  showAnswer && option === question.correctAnswer
                    ? 'bg-green-50 border-green-300 text-green-800'
                    : 'bg-gray-50 border-gray-200'
                }`}
              >
                <span className="font-medium mr-2">
                  {String.fromCharCode(65 + optionIndex)}.
                </span>
                {option}
              </div>
            ))}
          </div>
        )}

        {question.type === 'FILL_IN_BLANK' && showAnswer && (
          <div className="bg-green-50 border border-green-300 rounded-lg p-3">
            <span className="font-medium text-green-800">Answer: </span>
            <span className="text-green-700">{question.correctAnswer}</span>
          </div>
        )}

        {question.explanation && showAnswer && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <span className="font-medium text-blue-800">Explanation: </span>
            <span className="text-blue-700">{question.explanation}</span>
          </div>
        )}

        <div className="flex justify-between items-center mt-4 pt-4 border-t text-sm text-gray-600">
          <span>Difficulty: {question.difficulty}</span>
          <span>Points: {question.points}</span>
        </div>
      </CardContent>
    </Card>
  );
}
