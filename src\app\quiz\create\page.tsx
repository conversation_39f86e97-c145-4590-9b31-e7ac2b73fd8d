'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { ContentInput } from '@/components/quiz/content-input';
import { QuizConfiguration } from '@/components/quiz/quiz-configuration';
import { QuizPreview } from '@/components/quiz/quiz-preview';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, ArrowRight, Save, Wand2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';

type Step = 'input' | 'configure' | 'preview';

interface ContentData {
  content: string;
  metadata?: any;
  analysis?: any;
}

interface QuizConfig {
  title: string;
  description: string;
  questionCount: number;
  questionTypes: string[];
  difficulty: string;
  isPublic: boolean;
}

interface GeneratedQuestion {
  type: string;
  question: string;
  options: string[];
  correctAnswer: string;
  explanation: string;
  difficulty: string;
  points: number;
}

export default function CreateQuizPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();

  const [currentStep, setCurrentStep] = useState<Step>('input');
  const [contentData, setContentData] = useState<ContentData | null>(null);
  const [quizConfig, setQuizConfig] = useState<QuizConfig>({
    title: '',
    description: '',
    questionCount: 10,
    questionTypes: ['MULTIPLE_CHOICE'],
    difficulty: 'MEDIUM',
    isPublic: false,
  });
  const [generatedQuestions, setGeneratedQuestions] = useState<GeneratedQuestion[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const method = searchParams.get('method');

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  const handleContentSubmit = (data: ContentData) => {
    setContentData(data);
    setCurrentStep('configure');
    
    // Auto-populate title if available
    if (data.metadata?.title && !quizConfig.title) {
      setQuizConfig(prev => ({
        ...prev,
        title: data.metadata.title,
      }));
    }
  };

  const handleConfigSubmit = async (config: QuizConfig) => {
    setQuizConfig(config);
    setIsGenerating(true);

    try {
      const response = await fetch('/api/ai/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: contentData?.content,
          questionCount: config.questionCount,
          questionTypes: config.questionTypes,
          difficulty: config.difficulty,
          subject: config.title,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate questions');
      }

      const result = await response.json();
      setGeneratedQuestions(result.data.questions);
      setCurrentStep('preview');
      
      toast({
        title: 'Questions generated!',
        description: `Successfully generated ${result.data.questions.length} questions.`,
      });
    } catch (error) {
      console.error('Error generating questions:', error);
      toast({
        title: 'Generation failed',
        description: 'Failed to generate questions. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSaveQuiz = async () => {
    setIsSaving(true);

    try {
      const response = await fetch('/api/quiz', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: quizConfig.title,
          description: quizConfig.description,
          isPublic: quizConfig.isPublic,
          questions: generatedQuestions,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save quiz');
      }

      const result = await response.json();
      
      toast({
        title: 'Quiz saved!',
        description: 'Your quiz has been created successfully.',
      });

      router.push(`/quiz/${result.data.id}`);
    } catch (error) {
      console.error('Error saving quiz:', error);
      toast({
        title: 'Save failed',
        description: 'Failed to save quiz. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleBack = () => {
    if (currentStep === 'configure') {
      setCurrentStep('input');
    } else if (currentStep === 'preview') {
      setCurrentStep('configure');
    }
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="loading-spinner" />
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader user={session.user} />
      
      <main className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Link href="/dashboard">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900">Create New Quiz</h1>
          <p className="text-gray-600 mt-2">
            Transform your content into an engaging quiz with AI assistance
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center space-x-4">
            {[
              { key: 'input', label: 'Content Input', number: 1 },
              { key: 'configure', label: 'Configuration', number: 2 },
              { key: 'preview', label: 'Preview & Save', number: 3 },
            ].map((step) => (
              <div key={step.key} className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep === step.key
                      ? 'bg-blue-600 text-white'
                      : step.number < (['input', 'configure', 'preview'].indexOf(currentStep) + 1)
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  {step.number}
                </div>
                <span className="ml-2 text-sm font-medium text-gray-700">
                  {step.label}
                </span>
                {step.key !== 'preview' && (
                  <ArrowRight className="h-4 w-4 text-gray-400 ml-4" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Step Content */}
        <Card>
          <CardContent className="p-6">
            {currentStep === 'input' && (
              <ContentInput
                method={method}
                onSubmit={handleContentSubmit}
              />
            )}

            {currentStep === 'configure' && contentData && (
              <QuizConfiguration
                contentData={contentData}
                initialConfig={quizConfig}
                onSubmit={handleConfigSubmit}
                onBack={handleBack}
                isGenerating={isGenerating}
              />
            )}

            {currentStep === 'preview' && (
              <QuizPreview
                config={quizConfig}
                questions={generatedQuestions}
                onSave={handleSaveQuiz}
                onBack={handleBack}
                onUpdateQuestions={setGeneratedQuestions}
                isSaving={isSaving}
              />
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
