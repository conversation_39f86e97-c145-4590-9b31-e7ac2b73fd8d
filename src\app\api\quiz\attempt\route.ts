import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { z } from 'zod';

const submitAttemptSchema = z.object({
  quizId: z.string(),
  answers: z.array(z.object({
    questionId: z.string(),
    answer: z.string(),
  })),
  timeSpent: z.number().optional(),
});

// POST /api/quiz/attempt - Submit a quiz attempt
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = submitAttemptSchema.parse(body);

    // Get the quiz with questions
    const quiz = await prisma.quiz.findUnique({
      where: { id: validatedData.quizId },
      include: {
        questions: true,
      },
    });

    if (!quiz) {
      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 });
    }

    // Check if user can access this quiz
    const canAccess = quiz.isPublic || quiz.userId === session.user.id;
    if (!canAccess) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Calculate score
    let correctAnswers = 0;
    const answerDetails = validatedData.answers.map(answer => {
      const question = quiz.questions.find(q => q.id === answer.questionId);
      if (!question) {
        return {
          questionId: answer.questionId,
          answer: answer.answer,
          isCorrect: false,
        };
      }

      const isCorrect = answer.answer.trim().toLowerCase() === question.correctAnswer.trim().toLowerCase();
      if (isCorrect) correctAnswers++;

      return {
        questionId: answer.questionId,
        answer: answer.answer,
        isCorrect,
      };
    });

    const score = Math.round((correctAnswers / quiz.questions.length) * 100);

    // Save the attempt
    const attempt = await prisma.attempt.create({
      data: {
        quizId: validatedData.quizId,
        userId: session.user.id,
        score: correctAnswers,
        totalQuestions: quiz.questions.length,
        timeSpent: validatedData.timeSpent,
        answers: {
          create: answerDetails.map(detail => ({
            questionId: detail.questionId,
            answer: detail.answer,
            isCorrect: detail.isCorrect,
          })),
        },
      },
      include: {
        answers: {
          include: {
            question: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        attemptId: attempt.id,
        score,
        correctAnswers,
        totalQuestions: quiz.questions.length,
        timeSpent: validatedData.timeSpent,
      },
      message: 'Quiz attempt submitted successfully',
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error submitting quiz attempt:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
