'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>Lef<PERSON>, ArrowRight, Wand2 } from 'lucide-react';

interface QuizConfigurationProps {
  contentData: any;
  initialConfig: any;
  onSubmit: (config: any) => void;
  onBack: () => void;
  isGenerating: boolean;
}

export function QuizConfiguration({
  contentData,
  initialConfig,
  onSubmit,
  onBack,
  isGenerating,
}: QuizConfigurationProps) {
  const [config, setConfig] = useState(initialConfig);

  useEffect(() => {
    // Auto-populate based on content analysis
    if (contentData.analysis) {
      setConfig(prev => ({
        ...prev,
        questionCount: Math.min(contentData.analysis.suggestedQuestionCount || 10, 20),
        difficulty: contentData.analysis.recommendedDifficulty || 'MEDIUM',
      }));
    }
  }, [contentData]);

  const questionTypes = [
    { id: 'MULTIPLE_CHOICE', label: 'Multiple Choice', description: '4 options, 1 correct answer' },
    { id: 'TRUE_FALSE', label: 'True/False', description: 'Simple true or false questions' },
    { id: 'FILL_IN_BLANK', label: 'Fill in the Blank', description: 'Complete the missing word/phrase' },
    { id: 'SHORT_ANSWER', label: 'Short Answer', description: 'Brief written responses' },
  ];

  const difficultyLevels = [
    { id: 'REMEMBER', label: 'Remember', description: 'Recall facts and basic concepts', bloom: true },
    { id: 'UNDERSTAND', label: 'Understand', description: 'Explain ideas or concepts', bloom: true },
    { id: 'APPLY', label: 'Apply', description: 'Use information in new situations', bloom: true },
    { id: 'ANALYZE', label: 'Analyze', description: 'Draw connections among ideas', bloom: true },
    { id: 'EVALUATE', label: 'Evaluate', description: 'Justify a stand or decision', bloom: true },
    { id: 'CREATE', label: 'Create', description: 'Produce new or original work', bloom: true },
    { id: 'EASY', label: 'Easy', description: 'Basic level questions', bloom: false },
    { id: 'MEDIUM', label: 'Medium', description: 'Intermediate level questions', bloom: false },
    { id: 'HARD', label: 'Hard', description: 'Advanced level questions', bloom: false },
  ];

  const handleSubmit = () => {
    if (!config.title.trim()) {
      return;
    }
    if (config.questionTypes.length === 0) {
      return;
    }
    onSubmit(config);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Configure Your Quiz
        </h2>
        <p className="text-gray-600">
          Customize the settings for your AI-generated quiz
        </p>
      </div>

      {/* Content Summary */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-lg">Content Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Source:</span>
              <p className="text-gray-600 capitalize">{contentData.metadata?.source || 'Unknown'}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Word Count:</span>
              <p className="text-gray-600">{contentData.metadata?.wordCount || 0}</p>
            </div>
            {contentData.analysis?.keyConcepts && (
              <div className="col-span-2">
                <span className="font-medium text-gray-700">Key Concepts:</span>
                <p className="text-gray-600">
                  {contentData.analysis.keyConcepts.slice(0, 3).join(', ')}
                  {contentData.analysis.keyConcepts.length > 3 && '...'}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quiz Title *
              </label>
              <input
                type="text"
                value={config.title}
                onChange={(e) => setConfig(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter quiz title"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description (Optional)
              </label>
              <textarea
                value={config.description}
                onChange={(e) => setConfig(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Brief description of the quiz"
                rows={3}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Number of Questions
              </label>
              <div className="flex items-center space-x-4">
                <input
                  type="range"
                  min="5"
                  max="50"
                  value={config.questionCount}
                  onChange={(e) => setConfig(prev => ({ ...prev, questionCount: parseInt(e.target.value) }))}
                  className="flex-1"
                />
                <span className="text-lg font-medium text-gray-900 min-w-[3rem]">
                  {config.questionCount}
                </span>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                Recommended: {contentData.analysis?.suggestedQuestionCount || 10} questions
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isPublic"
                checked={config.isPublic}
                onChange={(e) => setConfig(prev => ({ ...prev, isPublic: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="isPublic" className="text-sm text-gray-700">
                Make this quiz public
              </label>
            </div>
          </CardContent>
        </Card>

        {/* Question Types */}
        <Card>
          <CardHeader>
            <CardTitle>Question Types</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {questionTypes.map((type) => (
                <div key={type.id} className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id={type.id}
                    checked={config.questionTypes.includes(type.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setConfig(prev => ({
                          ...prev,
                          questionTypes: [...prev.questionTypes, type.id]
                        }));
                      } else {
                        setConfig(prev => ({
                          ...prev,
                          questionTypes: prev.questionTypes.filter(t => t !== type.id)
                        }));
                      }
                    }}
                    className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <div>
                    <label htmlFor={type.id} className="text-sm font-medium text-gray-900">
                      {type.label}
                    </label>
                    <p className="text-sm text-gray-600">{type.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Difficulty Level */}
      <Card>
        <CardHeader>
          <CardTitle>Difficulty Level</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">Bloom's Taxonomy Levels</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {difficultyLevels.filter(d => d.bloom).map((level) => (
                  <button
                    key={level.id}
                    onClick={() => setConfig(prev => ({ ...prev, difficulty: level.id }))}
                    className={`p-3 text-left rounded-lg border-2 transition-all ${
                      config.difficulty === level.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium text-gray-900">{level.label}</div>
                    <div className="text-sm text-gray-600">{level.description}</div>
                  </button>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">Simple Levels</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {difficultyLevels.filter(d => !d.bloom).map((level) => (
                  <button
                    key={level.id}
                    onClick={() => setConfig(prev => ({ ...prev, difficulty: level.id }))}
                    className={`p-3 text-left rounded-lg border-2 transition-all ${
                      config.difficulty === level.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium text-gray-900">{level.label}</div>
                    <div className="text-sm text-gray-600">{level.description}</div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={!config.title.trim() || config.questionTypes.length === 0 || isGenerating}
        >
          {isGenerating ? (
            <>
              <Wand2 className="mr-2 h-4 w-4 animate-spin" />
              Generating Questions...
            </>
          ) : (
            <>
              Generate Questions
              <ArrowRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
