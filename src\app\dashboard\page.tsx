import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';
import { prisma } from '@/lib/db';
import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { DashboardStats } from '@/components/dashboard/dashboard-stats';
import { RecentQuizzes } from '@/components/dashboard/recent-quizzes';
import { QuickActions } from '@/components/dashboard/quick-actions';

export default async function DashboardPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    redirect('/auth/signin');
  }

  // Fetch dashboard data
  const [quizzes, totalQuizzes, totalAttempts] = await Promise.all([
    prisma.quiz.findMany({
      where: { userId: session.user.id },
      include: {
        questions: true,
        _count: {
          select: { attempts: true },
        },
      },
      orderBy: { updatedAt: 'desc' },
      take: 5,
    }),
    prisma.quiz.count({
      where: { userId: session.user.id },
    }),
    prisma.attempt.count({
      where: {
        quiz: { userId: session.user.id },
      },
    }),
  ]);

  // Calculate average score
  const attempts = await prisma.attempt.findMany({
    where: {
      quiz: { userId: session.user.id },
    },
    select: { score: true, totalQuestions: true },
  });

  const averageScore = attempts.length > 0
    ? Math.round(attempts.reduce((sum, attempt) => sum + (attempt.score / attempt.totalQuestions * 100), 0) / attempts.length)
    : 0;

  const stats = {
    totalQuizzes,
    totalAttempts,
    averageScore,
    recentActivity: [], // We'll implement this later
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader user={session.user} />
      
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {session.user.name?.split(' ')[0] || 'there'}!
          </h1>
          <p className="text-gray-600 mt-2">
            Here's what's happening with your quizzes today.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="lg:col-span-2">
            <DashboardStats stats={stats} />
          </div>
          <div>
            <QuickActions />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <RecentQuizzes quizzes={quizzes} />
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Getting Started
            </h2>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">1</span>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-900">Create your first quiz</h3>
                  <p className="text-sm text-gray-600">Upload a document or paste content to get started</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-green-600">2</span>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-900">Customize questions</h3>
                  <p className="text-sm text-gray-600">Edit AI-generated questions to match your needs</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-purple-600">3</span>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-900">Share and analyze</h3>
                  <p className="text-sm text-gray-600">Share your quiz and track performance</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
