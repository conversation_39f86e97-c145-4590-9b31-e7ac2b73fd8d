import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { formatRelativeTime } from '@/lib/utils';
import { Eye, Edit, Share2, MoreHorizontal } from 'lucide-react';
import Link from 'next/link';

interface Quiz {
  id: string;
  title: string;
  description?: string | null;
  updatedAt: Date;
  isPublic: boolean;
  questions: any[];
  _count: {
    attempts: number;
  };
}

interface RecentQuizzesProps {
  quizzes: Quiz[];
}

export function RecentQuizzes({ quizzes }: RecentQuizzesProps) {
  if (quizzes.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Quizzes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="text-gray-400 mb-4">
              <svg
                className="mx-auto h-12 w-12"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No quizzes yet
            </h3>
            <p className="text-gray-600 mb-4">
              Create your first quiz to get started
            </p>
            <Link href="/quiz/create">
              <Button>Create Quiz</Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Recent Quizzes</CardTitle>
        <Link href="/quiz">
          <Button variant="outline" size="sm">
            View All
          </Button>
        </Link>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {quizzes.map((quiz) => (
            <div
              key={quiz.id}
              className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <h3 className="text-sm font-medium text-gray-900 truncate">
                    {quiz.title}
                  </h3>
                  {quiz.isPublic && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                      Public
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                  <span>{quiz.questions.length} questions</span>
                  <span>{quiz._count.attempts} attempts</span>
                  <span>{formatRelativeTime(quiz.updatedAt)}</span>
                </div>
                {quiz.description && (
                  <p className="text-sm text-gray-600 mt-1 truncate">
                    {quiz.description}
                  </p>
                )}
              </div>
              
              <div className="flex items-center space-x-2 ml-4">
                <Link href={`/quiz/${quiz.id}`}>
                  <Button variant="ghost" size="sm">
                    <Eye className="h-4 w-4" />
                  </Button>
                </Link>
                <Link href={`/quiz/${quiz.id}/edit`}>
                  <Button variant="ghost" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                </Link>
                <Button variant="ghost" size="sm">
                  <Share2 className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
