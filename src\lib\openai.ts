import OpenAI from 'openai';

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing OPENAI_API_KEY environment variable');
}

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export const QUIZ_GENERATION_PROMPT = `
You are an expert quiz generator. Your task is to create high-quality, educational quiz questions based on the provided content.

Guidelines:
1. Generate questions that test understanding, not just memorization
2. Use Bloom's Taxonomy levels appropriately
3. Provide clear, concise questions
4. Include plausible distractors for multiple choice questions
5. Add helpful explanations for answers
6. Vary question difficulty based on the specified level

Question Types:
- MULTIPLE_CHOICE: 4 options, 1 correct answer
- TRUE_FALSE: True or false statement
- FILL_IN_BLANK: Question with a blank to fill
- SHORT_ANSWER: Open-ended question requiring a brief response

Difficulty Levels (<PERSON>'s Taxonomy):
- REMEMBER: Recall facts and basic concepts
- UNDERSTAND: Explain ideas or concepts
- APPLY: Use information in new situations
- ANALYZE: Draw connections among ideas
- EVALUATE: Justify a stand or decision
- CREATE: Produce new or original work

Return the response as a JSON array of question objects with this structure:
{
  "type": "MULTIPLE_CHOICE" | "TRUE_FALSE" | "FILL_IN_BLANK" | "SHORT_ANSWER",
  "question": "The question text",
  "options": ["option1", "option2", "option3", "option4"], // For multiple choice and true/false
  "correctAnswer": "The correct answer",
  "explanation": "Why this is the correct answer",
  "difficulty": "REMEMBER" | "UNDERSTAND" | "APPLY" | "ANALYZE" | "EVALUATE" | "CREATE",
  "points": 1 // Points for this question (1-3 based on difficulty)
}
`;

export async function generateQuizQuestions(
  content: string,
  options: {
    questionCount: number;
    questionTypes: string[];
    difficulty: string;
    subject?: string;
  }
) {
  const prompt = `
${QUIZ_GENERATION_PROMPT}

Content to generate questions from:
${content}

Requirements:
- Generate exactly ${options.questionCount} questions
- Use these question types: ${options.questionTypes.join(', ')}
- Target difficulty level: ${options.difficulty}
${options.subject ? `- Subject area: ${options.subject}` : ''}

Please generate the questions now.
`;

  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are an expert educational content creator specializing in quiz generation.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      temperature: 0.7,
      max_tokens: 2000,
    });

    const content_response = response.choices[0]?.message?.content;
    if (!content_response) {
      throw new Error('No response from OpenAI');
    }

    // Parse the JSON response
    const questions = JSON.parse(content_response);
    return questions;
  } catch (error) {
    console.error('Error generating quiz questions:', error);
    throw new Error('Failed to generate quiz questions');
  }
}

export async function extractKeyConceptsFromContent(content: string) {
  const prompt = `
Analyze the following content and extract the key concepts, topics, and learning objectives.
Return a JSON object with:
{
  "keyConcepts": ["concept1", "concept2", ...],
  "topics": ["topic1", "topic2", ...],
  "learningObjectives": ["objective1", "objective2", ...],
  "suggestedQuestionCount": number,
  "recommendedDifficulty": "EASY" | "MEDIUM" | "HARD"
}

Content:
${content}
`;

  try {
    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are an expert educational content analyzer.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      temperature: 0.3,
      max_tokens: 1000,
    });

    const content_response = response.choices[0]?.message?.content;
    if (!content_response) {
      throw new Error('No response from OpenAI');
    }

    return JSON.parse(content_response);
  } catch (error) {
    console.error('Error extracting key concepts:', error);
    throw new Error('Failed to extract key concepts');
  }
}
