'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Play, 
  Edit, 
  Share2, 
  Bar<PERSON>hart3, 
  Clock, 
  Users, 
  Trophy,
  ArrowLeft,
  CheckCircle,
  XCircle
} from 'lucide-react';
import Link from 'next/link';
import { formatRelativeTime, calculateQuizScore } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface QuizViewerProps {
  quiz: any;
  isOwner: boolean;
  user?: any;
}

export function QuizViewer({ quiz, isOwner, user }: QuizViewerProps) {
  const [isStarted, setIsStarted] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<{ [key: number]: string }>({});
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [startTime, setStartTime] = useState<Date | null>(null);
  const { toast } = useToast();

  const handleStartQuiz = () => {
    setIsStarted(true);
    setStartTime(new Date());
  };

  const handleAnswerSelect = (questionIndex: number, answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionIndex]: answer,
    }));
  };

  const handleSubmitQuiz = async () => {
    if (!user) {
      toast({
        title: 'Sign in required',
        description: 'Please sign in to submit your quiz answers.',
        variant: 'destructive',
      });
      return;
    }

    const endTime = new Date();
    const timeSpent = startTime ? Math.floor((endTime.getTime() - startTime.getTime()) / 1000) : 0;

    // Calculate results
    let correctAnswers = 0;
    const answerDetails = quiz.questions.map((question: any, index: number) => {
      const userAnswer = answers[index] || '';
      const isCorrect = userAnswer === question.correctAnswer;
      if (isCorrect) correctAnswers++;

      return {
        questionId: question.id,
        question: question.question,
        userAnswer,
        correctAnswer: question.correctAnswer,
        isCorrect,
        explanation: question.explanation,
      };
    });

    const score = calculateQuizScore(quiz.questions.length, correctAnswers);

    try {
      // Save attempt to database
      const response = await fetch('/api/quiz/attempt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          quizId: quiz.id,
          answers: Object.entries(answers).map(([questionIndex, answer]) => ({
            questionId: quiz.questions[parseInt(questionIndex)].id,
            answer,
          })),
          timeSpent,
        }),
      });

      if (response.ok) {
        setResults({
          score,
          totalQuestions: quiz.questions.length,
          correctAnswers,
          timeSpent,
          answers: answerDetails,
        });
        setIsSubmitted(true);
      }
    } catch (error) {
      console.error('Error submitting quiz:', error);
      // Still show results even if saving fails
      setResults({
        score,
        totalQuestions: quiz.questions.length,
        correctAnswers,
        timeSpent,
        answers: answerDetails,
      });
      setIsSubmitted(true);
    }
  };

  const handleShareQuiz = async () => {
    const shareUrl = `${window.location.origin}/quiz/${quiz.id}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: quiz.title,
          text: quiz.description || 'Check out this quiz!',
          url: shareUrl,
        });
      } catch (error) {
        // Fallback to clipboard
        navigator.clipboard.writeText(shareUrl);
        toast({
          title: 'Link copied!',
          description: 'Quiz link has been copied to your clipboard.',
        });
      }
    } else {
      navigator.clipboard.writeText(shareUrl);
      toast({
        title: 'Link copied!',
        description: 'Quiz link has been copied to your clipboard.',
      });
    }
  };

  if (isSubmitted && results) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <div className="mb-4">
            <Trophy className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900">Quiz Complete!</h1>
            <p className="text-gray-600 mt-2">Here are your results</p>
          </div>
        </div>

        <Card className="text-center">
          <CardContent className="p-8">
            <div className="text-6xl font-bold text-blue-600 mb-2">
              {results.score}%
            </div>
            <div className="text-lg text-gray-600 mb-4">
              {results.correctAnswers} out of {results.totalQuestions} correct
            </div>
            <div className="text-sm text-gray-500">
              Completed in {Math.floor(results.timeSpent / 60)}m {results.timeSpent % 60}s
            </div>
          </CardContent>
        </Card>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-900">Review Answers</h2>
          {results.answers.map((answer: any, index: number) => (
            <Card key={index} className={`border-l-4 ${
              answer.isCorrect ? 'border-l-green-500' : 'border-l-red-500'
            }`}>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  {answer.isCorrect ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-600" />
                  )}
                  <span>Question {index + 1}</span>
                </CardTitle>
                <p className="text-gray-700">{answer.question}</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <span className="font-medium text-gray-700">Your answer: </span>
                    <span className={answer.isCorrect ? 'text-green-600' : 'text-red-600'}>
                      {answer.userAnswer || 'No answer'}
                    </span>
                  </div>
                  {!answer.isCorrect && (
                    <div>
                      <span className="font-medium text-gray-700">Correct answer: </span>
                      <span className="text-green-600">{answer.correctAnswer}</span>
                    </div>
                  )}
                  {answer.explanation && (
                    <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                      <span className="font-medium text-blue-800">Explanation: </span>
                      <span className="text-blue-700">{answer.explanation}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="flex justify-center space-x-4">
          <Link href="/dashboard">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Button>
          </Link>
          <Button onClick={() => window.location.reload()}>
            Take Quiz Again
          </Button>
        </div>
      </div>
    );
  }

  if (isStarted) {
    const question = quiz.questions[currentQuestion];
    const progress = ((currentQuestion + 1) / quiz.questions.length) * 100;

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">{quiz.title}</h1>
          <div className="text-sm text-gray-600">
            Question {currentQuestion + 1} of {quiz.questions.length}
          </div>
        </div>

        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">
              {question.question}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {(question.type === 'MULTIPLE_CHOICE' || question.type === 'TRUE_FALSE') && (
              <div className="space-y-3">
                {question.options.map((option: string, index: number) => (
                  <button
                    key={index}
                    onClick={() => handleAnswerSelect(currentQuestion, option)}
                    className={`w-full text-left p-4 rounded-lg border-2 transition-all ${
                      answers[currentQuestion] === option
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <span className="font-medium mr-3">
                      {String.fromCharCode(65 + index)}.
                    </span>
                    {option}
                  </button>
                ))}
              </div>
            )}

            {question.type === 'FILL_IN_BLANK' && (
              <input
                type="text"
                value={answers[currentQuestion] || ''}
                onChange={(e) => handleAnswerSelect(currentQuestion, e.target.value)}
                placeholder="Enter your answer..."
                className="w-full p-4 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:outline-none"
              />
            )}

            {question.type === 'SHORT_ANSWER' && (
              <textarea
                value={answers[currentQuestion] || ''}
                onChange={(e) => handleAnswerSelect(currentQuestion, e.target.value)}
                placeholder="Enter your answer..."
                rows={4}
                className="w-full p-4 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:outline-none resize-none"
              />
            )}
          </CardContent>
        </Card>

        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}
            disabled={currentQuestion === 0}
          >
            Previous
          </Button>
          
          {currentQuestion === quiz.questions.length - 1 ? (
            <Button onClick={handleSubmitQuiz}>
              Submit Quiz
            </Button>
          ) : (
            <Button
              onClick={() => setCurrentQuestion(currentQuestion + 1)}
              disabled={!answers[currentQuestion]}
            >
              Next
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{quiz.title}</h1>
          {quiz.description && (
            <p className="text-gray-600 mt-2">{quiz.description}</p>
          )}
        </div>
        {isOwner && (
          <div className="flex space-x-2">
            <Link href={`/quiz/${quiz.id}/edit`}>
              <Button variant="outline" size="sm">
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </Link>
            <Button variant="outline" size="sm" onClick={handleShareQuiz}>
              <Share2 className="mr-2 h-4 w-4" />
              Share
            </Button>
            <Link href={`/quiz/${quiz.id}/analytics`}>
              <Button variant="outline" size="sm">
                <BarChart3 className="mr-2 h-4 w-4" />
                Analytics
              </Button>
            </Link>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6 text-center">
            <Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">
              {quiz.questions.length}
            </div>
            <div className="text-sm text-gray-600">Questions</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">
              {quiz._count.attempts}
            </div>
            <div className="text-sm text-gray-600">Attempts</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6 text-center">
            <Trophy className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">
              {quiz.isPublic ? 'Public' : 'Private'}
            </div>
            <div className="text-sm text-gray-600">Visibility</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Quiz Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Created by:</span>
              <p className="text-gray-900">{quiz.user.name}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Created:</span>
              <p className="text-gray-900">{formatRelativeTime(quiz.createdAt)}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Last updated:</span>
              <p className="text-gray-900">{formatRelativeTime(quiz.updatedAt)}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">Question types:</span>
              <p className="text-gray-900">
                {[...new Set(quiz.questions.map((q: any) => q.type))].join(', ')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="text-center">
        <Button size="lg" onClick={handleStartQuiz}>
          <Play className="mr-2 h-5 w-5" />
          Start Quiz
        </Button>
      </div>
    </div>
  );
}
