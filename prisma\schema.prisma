// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts Account[]
  sessions Session[]
  quizzes  Quiz[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Quiz {
  id          String   @id @default(cuid())
  title       String
  description String?
  userId      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  isPublic    Boolean  @default(false)
  shareToken  String?  @unique

  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  questions Question[]
  attempts  Attempt[]

  @@index([userId])
  @@index([shareToken])
}

model Question {
  id       String      @id @default(cuid())
  quizId   String
  type     QuestionType
  question String
  options  String[]    @default([])
  correctAnswer String
  explanation String?
  difficulty DifficultyLevel @default(MEDIUM)
  order    Int
  points   Int         @default(1)

  quiz Quiz @relation(fields: [quizId], references: [id], onDelete: Cascade)
  answers Answer[]

  @@index([quizId])
}

model Attempt {
  id        String   @id @default(cuid())
  quizId    String
  userId    String?
  score     Int
  totalQuestions Int
  completedAt DateTime @default(now())
  timeSpent Int? // in seconds

  quiz Quiz @relation(fields: [quizId], references: [id], onDelete: Cascade)
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)
  answers Answer[]

  @@index([quizId])
  @@index([userId])
}

model Answer {
  id         String @id @default(cuid())
  attemptId  String
  questionId String
  answer     String
  isCorrect  Boolean
  timeSpent  Int? // in seconds

  attempt  Attempt  @relation(fields: [attemptId], references: [id], onDelete: Cascade)
  question Question @relation(fields: [questionId], references: [id], onDelete: Cascade)

  @@unique([attemptId, questionId])
}

model UploadedFile {
  id        String   @id @default(cuid())
  userId    String
  filename  String
  originalName String
  mimeType  String
  size      Int
  url       String
  createdAt DateTime @default(now())

  @@index([userId])
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  FILL_IN_BLANK
  SHORT_ANSWER
}

enum DifficultyLevel {
  REMEMBER    // Bloom's Taxonomy Level 1
  UNDERSTAND  // Bloom's Taxonomy Level 2
  APPLY       // Bloom's Taxonomy Level 3
  ANALYZE     // Bloom's Taxonomy Level 4
  EVALUATE    // Bloom's Taxonomy Level 5
  CREATE      // Bloom's Taxonomy Level 6
  EASY        // Simple mapping
  MEDIUM      // Simple mapping
  HARD        // Simple mapping
}
