import { Quiz, Question, User, Attempt, Answer } from '@prisma/client';

export type QuizWithQuestions = Quiz & {
  questions: Question[];
  user: User;
  _count?: {
    attempts: number;
  };
};

export type QuizWithDetails = Quiz & {
  questions: Question[];
  user: User;
  attempts: (Attempt & {
    user: User | null;
    answers: Answer[];
  })[];
};

export type AttemptWithDetails = Attempt & {
  quiz: Quiz;
  user: User | null;
  answers: (Answer & {
    question: Question;
  })[];
};

export type QuestionWithAnswers = Question & {
  answers: Answer[];
};

export interface QuizFormData {
  title: string;
  description?: string;
  isPublic: boolean;
}

export interface QuestionFormData {
  type: QuestionType;
  question: string;
  options: string[];
  correctAnswer: string;
  explanation?: string;
  difficulty: DifficultyLevel;
  points: number;
}

export interface QuizGenerationOptions {
  questionCount: number;
  questionTypes: QuestionType[];
  difficulty: DifficultyLevel;
  subject?: string;
}

export interface ContentParsingResult {
  content: string;
  metadata: {
    title?: string;
    author?: string;
    pageCount?: number;
    wordCount: number;
  };
}

export interface KeyConceptsResult {
  keyConcepts: string[];
  topics: string[];
  learningObjectives: string[];
  suggestedQuestionCount: number;
  recommendedDifficulty: 'EASY' | 'MEDIUM' | 'HARD';
}

export interface QuizAttemptData {
  answers: {
    questionId: string;
    answer: string;
    timeSpent?: number;
  }[];
  totalTimeSpent?: number;
}

export interface QuizResults {
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  timeSpent: number;
  answers: {
    questionId: string;
    question: string;
    userAnswer: string;
    correctAnswer: string;
    isCorrect: boolean;
    explanation?: string;
  }[];
}

export interface DashboardStats {
  totalQuizzes: number;
  totalAttempts: number;
  averageScore: number;
  recentActivity: {
    id: string;
    type: 'quiz_created' | 'quiz_attempted' | 'quiz_shared';
    title: string;
    timestamp: Date;
  }[];
}

export interface FileUploadResult {
  id: string;
  filename: string;
  url: string;
  size: number;
  mimeType: string;
}

export enum QuestionType {
  MULTIPLE_CHOICE = 'MULTIPLE_CHOICE',
  TRUE_FALSE = 'TRUE_FALSE',
  FILL_IN_BLANK = 'FILL_IN_BLANK',
  SHORT_ANSWER = 'SHORT_ANSWER',
}

export enum DifficultyLevel {
  REMEMBER = 'REMEMBER',
  UNDERSTAND = 'UNDERSTAND',
  APPLY = 'APPLY',
  ANALYZE = 'ANALYZE',
  EVALUATE = 'EVALUATE',
  CREATE = 'CREATE',
  EASY = 'EASY',
  MEDIUM = 'MEDIUM',
  HARD = 'HARD',
}

export interface QuizTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  questionTypes: QuestionType[];
  defaultQuestionCount: number;
  difficulty: DifficultyLevel;
  tags: string[];
}

export interface AnalyticsData {
  quizId: string;
  totalAttempts: number;
  averageScore: number;
  completionRate: number;
  averageTimeSpent: number;
  questionAnalytics: {
    questionId: string;
    question: string;
    correctRate: number;
    averageTimeSpent: number;
    commonWrongAnswers: string[];
  }[];
  timeSeriesData: {
    date: string;
    attempts: number;
    averageScore: number;
  }[];
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends APIResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Next.js API types
export interface NextApiRequestWithUser extends Request {
  user?: User;
}

// Form validation schemas
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormState<T> {
  data: T;
  errors: ValidationError[];
  isSubmitting: boolean;
  isValid: boolean;
}
