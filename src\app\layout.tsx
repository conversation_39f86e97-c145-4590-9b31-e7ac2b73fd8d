import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from '@/components/providers';
import { Toaster } from '@/components/ui/toaster';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'QuizCraft AI - AI-Powered Quiz Generation Platform',
  description: 'Transform any content into engaging quizzes with the power of AI. Create, share, and analyze quizzes effortlessly.',
  keywords: ['quiz', 'AI', 'education', 'learning', 'assessment', 'OpenAI', 'quiz generator'],
  authors: [{ name: 'QuizCraft AI Team' }],
  creator: 'QuizCraft AI',
  publisher: 'QuizCraft AI',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXTAUTH_URL || 'http://localhost:3000'),
  openGraph: {
    title: 'QuizCraft AI - AI-Powered Quiz Generation Platform',
    description: 'Transform any content into engaging quizzes with the power of AI',
    url: '/',
    siteName: 'QuizCraft AI',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'QuizCraft AI - AI-Powered Quiz Generation Platform',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'QuizCraft AI - AI-Powered Quiz Generation Platform',
    description: 'Transform any content into engaging quizzes with the power of AI',
    images: ['/og-image.png'],
    creator: '@quizcraft_ai',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          <div className="min-h-screen bg-background">
            {children}
          </div>
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}
