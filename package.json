{"name": "quizcraft-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "postinstall": "prisma generate"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.6.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "@uploadthing/react": "^6.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "mammoth": "^1.6.0", "next": "14.0.3", "next-auth": "^4.24.5", "openai": "^4.20.1", "pdf-parse": "^1.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^6.0.4", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20.9.0", "@types/pdf-parse": "^1.1.4", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-next": "14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "postcss": "^8.4.31", "prettier": "^3.1.0", "prisma": "^5.6.0", "tailwindcss": "^3.3.5", "tsx": "^4.6.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.10.0"}