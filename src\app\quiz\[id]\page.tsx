import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/db';
import { notFound } from 'next/navigation';
import { QuizViewer } from '@/components/quiz/quiz-viewer';
import { DashboardHeader } from '@/components/dashboard/dashboard-header';

interface QuizPageProps {
  params: { id: string };
}

export default async function QuizPage({ params }: QuizPageProps) {
  const session = await getServerSession(authOptions);
  const quizId = params.id;

  const quiz = await prisma.quiz.findUnique({
    where: { id: quizId },
    include: {
      questions: {
        orderBy: { order: 'asc' },
      },
      user: {
        select: { id: true, name: true, image: true },
      },
      _count: {
        select: { attempts: true },
      },
    },
  });

  if (!quiz) {
    notFound();
  }

  // Check if user can access this quiz
  const canAccess = quiz.isPublic || quiz.userId === session?.user?.id;
  if (!canAccess) {
    return (
      <div className="min-h-screen bg-gray-50">
        {session && <DashboardHeader user={session.user} />}
        <main className="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Access Denied
            </h1>
            <p className="text-gray-600">
              This quiz is private and you don't have permission to view it.
            </p>
          </div>
        </main>
      </div>
    );
  }

  // Check if user is the owner
  const isOwner = quiz.userId === session?.user?.id;

  return (
    <div className="min-h-screen bg-gray-50">
      {session && <DashboardHeader user={session.user} />}
      <main className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <QuizViewer
          quiz={quiz}
          isOwner={isOwner}
          user={session?.user}
        />
      </main>
    </div>
  );
}
