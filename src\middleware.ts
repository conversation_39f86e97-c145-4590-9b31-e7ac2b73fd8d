import { withAuth } from 'next-auth/middleware';

export default withAuth(
  function middleware(req) {
    // Add any additional middleware logic here
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Protect dashboard and quiz creation routes
        if (req.nextUrl.pathname.startsWith('/dashboard') || 
            req.nextUrl.pathname.startsWith('/quiz/create') ||
            req.nextUrl.pathname.includes('/edit')) {
          return !!token;
        }
        return true;
      },
    },
  }
);

export const config = {
  matcher: [
    '/dashboard/:path*',
    '/quiz/create/:path*',
    '/quiz/:path*/edit',
    '/profile/:path*',
    '/settings/:path*',
  ],
};
