import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';
import { prisma } from '@/lib/db';
import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { QuizList } from '@/components/quiz/quiz-list';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import Link from 'next/link';

interface QuizPageProps {
  searchParams: { [key: string]: string | string[] | undefined };
}

export default async function QuizPage({ searchParams }: QuizPageProps) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    redirect('/auth/signin');
  }

  const page = parseInt((searchParams.page as string) || '1');
  const limit = parseInt((searchParams.limit as string) || '12');
  const search = (searchParams.search as string) || '';

  const skip = (page - 1) * limit;

  const where = {
    userId: session.user.id,
    ...(search && {
      OR: [
        { title: { contains: search, mode: 'insensitive' as const } },
        { description: { contains: search, mode: 'insensitive' as const } },
      ],
    }),
  };

  const [quizzes, total] = await Promise.all([
    prisma.quiz.findMany({
      where,
      include: {
        questions: true,
        _count: {
          select: { attempts: true },
        },
      },
      orderBy: { updatedAt: 'desc' },
      skip,
      take: limit,
    }),
    prisma.quiz.count({ where }),
  ]);

  const totalPages = Math.ceil(total / limit);

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader user={session.user} />
      
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">My Quizzes</h1>
            <p className="text-gray-600 mt-2">
              Manage and organize your quiz collection
            </p>
          </div>
          <Link href="/quiz/create">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Quiz
            </Button>
          </Link>
        </div>

        <QuizList
          quizzes={quizzes}
          currentPage={page}
          totalPages={totalPages}
          searchQuery={search}
        />
      </main>
    </div>
  );
}
