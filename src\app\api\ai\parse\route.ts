import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { extractKeyConceptsFromContent } from '@/lib/openai';
import { z } from 'zod';
import pdfParse from 'pdf-parse';
import mammoth from 'mammoth';

const parseContentSchema = z.object({
  content: z.string().optional(),
  url: z.string().url().optional(),
  fileData: z.string().optional(), // Base64 encoded file data
  fileName: z.string().optional(),
  mimeType: z.string().optional(),
}).refine(
  (data) => data.content || data.url || data.fileData,
  { message: 'Either content, url, or fileData must be provided' }
);

// POST /api/ai/parse - Parse content and extract key concepts
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = parseContentSchema.parse(body);

    let extractedContent = '';
    let metadata: any = {};

    // Handle different input types
    if (validatedData.content) {
      extractedContent = validatedData.content;
      metadata = {
        wordCount: extractedContent.split(' ').length,
        source: 'direct_input',
      };
    } else if (validatedData.url) {
      // Parse URL content
      try {
        const response = await fetch(validatedData.url);
        const html = await response.text();
        
        // Simple HTML text extraction (in production, use a proper HTML parser)
        extractedContent = html
          .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
          .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
          .replace(/<[^>]*>/g, ' ')
          .replace(/\s+/g, ' ')
          .trim();

        metadata = {
          url: validatedData.url,
          wordCount: extractedContent.split(' ').length,
          source: 'url',
        };
      } catch (error) {
        return NextResponse.json(
          { error: 'Failed to fetch content from URL' },
          { status: 400 }
        );
      }
    } else if (validatedData.fileData && validatedData.mimeType) {
      // Parse file content
      try {
        const buffer = Buffer.from(validatedData.fileData, 'base64');
        
        if (validatedData.mimeType === 'application/pdf') {
          const pdfData = await pdfParse(buffer);
          extractedContent = pdfData.text;
          metadata = {
            fileName: validatedData.fileName,
            pageCount: pdfData.numpages,
            wordCount: extractedContent.split(' ').length,
            source: 'pdf',
          };
        } else if (
          validatedData.mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ) {
          const docxData = await mammoth.extractRawText({ buffer });
          extractedContent = docxData.value;
          metadata = {
            fileName: validatedData.fileName,
            wordCount: extractedContent.split(' ').length,
            source: 'docx',
          };
        } else if (validatedData.mimeType.startsWith('text/')) {
          extractedContent = buffer.toString('utf-8');
          metadata = {
            fileName: validatedData.fileName,
            wordCount: extractedContent.split(' ').length,
            source: 'text',
          };
        } else {
          return NextResponse.json(
            { error: 'Unsupported file type' },
            { status: 400 }
          );
        }
      } catch (error) {
        console.error('File parsing error:', error);
        return NextResponse.json(
          { error: 'Failed to parse file content' },
          { status: 400 }
        );
      }
    }

    if (!extractedContent || extractedContent.length < 50) {
      return NextResponse.json(
        { error: 'Insufficient content extracted' },
        { status: 400 }
      );
    }

    // Extract key concepts using AI
    let keyConceptsResult;
    try {
      keyConceptsResult = await extractKeyConceptsFromContent(extractedContent);
    } catch (error) {
      console.error('Key concepts extraction error:', error);
      // Provide fallback analysis
      keyConceptsResult = {
        keyConcepts: extractSimpleKeywords(extractedContent),
        topics: ['General Topic'],
        learningObjectives: ['Understand the main concepts'],
        suggestedQuestionCount: Math.min(10, Math.floor(extractedContent.split(' ').length / 100)),
        recommendedDifficulty: 'MEDIUM',
      };
    }

    return NextResponse.json({
      success: true,
      data: {
        content: extractedContent,
        metadata,
        analysis: keyConceptsResult,
      },
      message: 'Content parsed and analyzed successfully',
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error parsing content:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Simple keyword extraction fallback
function extractSimpleKeywords(content: string): string[] {
  const words = content
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 4);

  const wordCount: { [key: string]: number } = {};
  words.forEach(word => {
    wordCount[word] = (wordCount[word] || 0) + 1;
  });

  return Object.entries(wordCount)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([word]) => word);
}
