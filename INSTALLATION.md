# QuizCraft AI - Installation Guide

This guide will help you set up QuizCraft AI locally for development or production.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** 18.0 or later
- **PostgreSQL** 14.0 or later
- **pnpm** (recommended) or npm
- **Git**

## Quick Start

### 1. <PERSON>lone the Repository

```bash
git clone https://github.com/HectorTa1989/quizcraft-ai.git
cd quizcraft-ai
```

### 2. Install Dependencies

```bash
pnpm install
```

### 3. Set Up Environment Variables

Copy the example environment file:

```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/quizcraft"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# OpenAI
OPENAI_API_KEY="your-openai-api-key"

# File Upload (UploadThing)
UPLOADTHING_SECRET="your-uploadthing-secret"
UPLOADTHING_APP_ID="your-uploadthing-app-id"
```

### 4. Set Up the Database

Create a PostgreSQL database:

```sql
CREATE DATABASE quizcraft;
```

Run database migrations:

```bash
pnpm db:push
```

Seed the database with sample data:

```bash
pnpm db:seed
```

### 5. Start the Development Server

```bash
pnpm dev
```

Visit `http://localhost:3000` to see your application!

## Detailed Setup Instructions

### Database Setup

#### Option 1: Local PostgreSQL

1. Install PostgreSQL on your system
2. Create a new database:
   ```sql
   CREATE DATABASE quizcraft;
   CREATE USER quizcraft_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE quizcraft TO quizcraft_user;
   ```
3. Update your `DATABASE_URL` in `.env.local`

#### Option 2: Cloud Database (Recommended for Production)

Use services like:
- **Supabase** (Free tier available)
- **PlanetScale** (Free tier available)
- **Railway** (Free tier available)
- **Neon** (Free tier available)

### Authentication Setup

#### Google OAuth

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `https://yourdomain.com/api/auth/callback/google` (production)

#### GitHub OAuth

1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create a new OAuth App
3. Set Authorization callback URL:
   - `http://localhost:3000/api/auth/callback/github` (development)
   - `https://yourdomain.com/api/auth/callback/github` (production)

### OpenAI API Setup

1. Sign up at [OpenAI](https://platform.openai.com/)
2. Create an API key
3. Add billing information (required for GPT-4 access)
4. Add the API key to your `.env.local`

### File Upload Setup (Optional)

For file upload functionality, you can use:

#### Option 1: UploadThing (Recommended)

1. Sign up at [UploadThing](https://uploadthing.com/)
2. Create a new app
3. Get your App ID and Secret
4. Add them to your `.env.local`

#### Option 2: AWS S3

1. Create an S3 bucket
2. Create IAM user with S3 permissions
3. Update the file upload configuration in the code

## Production Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy!

### Docker Deployment

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

### Environment Variables for Production

Make sure to set these in your production environment:

```env
NODE_ENV=production
NEXTAUTH_URL=https://yourdomain.com
DATABASE_URL=your-production-database-url
NEXTAUTH_SECRET=your-production-secret
# ... other variables
```

## Development Commands

```bash
# Start development server
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start

# Run linting
pnpm lint

# Fix linting issues
pnpm lint:fix

# Type checking
pnpm type-check

# Database commands
pnpm db:generate    # Generate Prisma client
pnpm db:push        # Push schema to database
pnpm db:migrate     # Run migrations
pnpm db:seed        # Seed database
pnpm db:studio      # Open Prisma Studio
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check your DATABASE_URL format
   - Ensure PostgreSQL is running
   - Verify database credentials

2. **OAuth Authentication Issues**
   - Check redirect URIs match exactly
   - Verify client ID and secret
   - Ensure OAuth apps are configured correctly

3. **OpenAI API Errors**
   - Verify API key is correct
   - Check billing is set up
   - Ensure you have GPT-4 access

4. **Build Errors**
   - Clear `.next` folder: `rm -rf .next`
   - Clear node_modules: `rm -rf node_modules && pnpm install`
   - Check TypeScript errors: `pnpm type-check`

### Getting Help

- Check the [GitHub Issues](https://github.com/HectorTa1989/quizcraft-ai/issues)
- Review the documentation
- Join our community discussions

## Next Steps

After installation:

1. Create your first quiz
2. Explore the dashboard
3. Try different question types
4. Share your quizzes
5. Analyze quiz performance

## Contributing

See [CONTRIBUTING.md](CONTRIBUTING.md) for development guidelines.

## License

This project is licensed under the MIT License - see [LICENSE](LICENSE) for details.
