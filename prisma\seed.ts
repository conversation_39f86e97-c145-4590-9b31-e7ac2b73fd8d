import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Create a demo user
  const demoUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Demo User',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
    },
  });

  // Create sample quizzes
  const sampleQuiz1 = await prisma.quiz.upsert({
    where: { id: 'sample-quiz-1' },
    update: {},
    create: {
      id: 'sample-quiz-1',
      title: 'JavaScript Fundamentals',
      description: 'Test your knowledge of JavaScript basics',
      userId: demoUser.id,
      isPublic: true,
      shareToken: 'js-fundamentals-demo',
    },
  });

  const sampleQuiz2 = await prisma.quiz.upsert({
    where: { id: 'sample-quiz-2' },
    update: {},
    create: {
      id: 'sample-quiz-2',
      title: 'React Concepts',
      description: 'Understanding React components and hooks',
      userId: demoUser.id,
      isPublic: true,
      shareToken: 'react-concepts-demo',
    },
  });

  // Create sample questions for JavaScript quiz
  const jsQuestions = [
    {
      type: 'MULTIPLE_CHOICE',
      question: 'What is the correct way to declare a variable in JavaScript?',
      options: ['var x = 5;', 'variable x = 5;', 'v x = 5;', 'declare x = 5;'],
      correctAnswer: 'var x = 5;',
      explanation: 'In JavaScript, variables can be declared using var, let, or const keywords.',
      difficulty: 'EASY',
      order: 1,
      points: 1,
    },
    {
      type: 'TRUE_FALSE',
      question: 'JavaScript is a statically typed language.',
      options: ['True', 'False'],
      correctAnswer: 'False',
      explanation: 'JavaScript is a dynamically typed language, meaning variable types are determined at runtime.',
      difficulty: 'MEDIUM',
      order: 2,
      points: 1,
    },
    {
      type: 'FILL_IN_BLANK',
      question: 'The _____ operator is used to check both value and type equality in JavaScript.',
      options: [],
      correctAnswer: '===',
      explanation: 'The strict equality operator (===) checks both value and type, while == only checks value.',
      difficulty: 'MEDIUM',
      order: 3,
      points: 2,
    },
  ];

  for (const questionData of jsQuestions) {
    await prisma.question.create({
      data: {
        ...questionData,
        quizId: sampleQuiz1.id,
      },
    });
  }

  // Create sample questions for React quiz
  const reactQuestions = [
    {
      type: 'MULTIPLE_CHOICE',
      question: 'What is JSX?',
      options: [
        'A JavaScript library',
        'A syntax extension for JavaScript',
        'A database query language',
        'A CSS framework',
      ],
      correctAnswer: 'A syntax extension for JavaScript',
      explanation: 'JSX is a syntax extension for JavaScript that allows you to write HTML-like code in your JavaScript files.',
      difficulty: 'EASY',
      order: 1,
      points: 1,
    },
    {
      type: 'TRUE_FALSE',
      question: 'React components must always return a single parent element.',
      options: ['True', 'False'],
      correctAnswer: 'False',
      explanation: 'With React Fragments or React 16+, components can return multiple elements without a wrapper.',
      difficulty: 'MEDIUM',
      order: 2,
      points: 1,
    },
  ];

  for (const questionData of reactQuestions) {
    await prisma.question.create({
      data: {
        ...questionData,
        quizId: sampleQuiz2.id,
      },
    });
  }

  console.log('✅ Database seeded successfully!');
  console.log(`👤 Demo user created: ${demoUser.email}`);
  console.log(`📝 Sample quizzes created: ${sampleQuiz1.title}, ${sampleQuiz2.title}`);
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
