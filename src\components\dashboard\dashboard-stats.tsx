import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart3, FileText, Users, TrendingUp } from 'lucide-react';

interface DashboardStatsProps {
  stats: {
    totalQuizzes: number;
    totalAttempts: number;
    averageScore: number;
    recentActivity: any[];
  };
}

export function DashboardStats({ stats }: DashboardStatsProps) {
  const statCards = [
    {
      title: 'Total Quizzes',
      value: stats.totalQuizzes,
      icon: FileText,
      description: 'Quizzes created',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Total Attempts',
      value: stats.totalAttempts,
      icon: Users,
      description: 'Quiz attempts',
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Average Score',
      value: `${stats.averageScore}%`,
      icon: TrendingUp,
      description: 'Across all quizzes',
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      title: 'Analytics',
      value: 'View',
      icon: BarChart3,
      description: 'Detailed insights',
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {statCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">
                {stat.value}
              </div>
              <p className="text-xs text-gray-600 mt-1">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
