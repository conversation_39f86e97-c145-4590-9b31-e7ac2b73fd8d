import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { generateQuizQuestions } from '@/lib/openai';
import { z } from 'zod';

const generateQuizSchema = z.object({
  content: z.string().min(50, 'Content must be at least 50 characters'),
  questionCount: z.number().min(1).max(50).default(10),
  questionTypes: z.array(z.enum(['MULTIPLE_CHOICE', 'TRUE_FALSE', 'FILL_IN_BLANK', 'SHORT_ANSWER'])).min(1),
  difficulty: z.enum(['REMEMBER', 'UNDERSTAND', 'APPLY', 'ANALYZE', 'EVALUATE', 'CREATE', 'EASY', 'MEDIUM', 'HARD']).default('MEDIUM'),
  subject: z.string().optional(),
});

// POST /api/ai/generate - Generate quiz questions from content
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = generateQuizSchema.parse(body);

    // Rate limiting check (simple implementation)
    // In production, you'd want to use Redis or a proper rate limiting service
    const rateLimitKey = `quiz_generation_${session.user.id}`;
    // This is a simplified rate limit - implement proper rate limiting in production

    try {
      const questions = await generateQuizQuestions(validatedData.content, {
        questionCount: validatedData.questionCount,
        questionTypes: validatedData.questionTypes,
        difficulty: validatedData.difficulty,
        subject: validatedData.subject,
      });

      // Validate the generated questions
      const validatedQuestions = questions.map((question: any, index: number) => {
        // Ensure the question has all required fields
        return {
          type: question.type || 'MULTIPLE_CHOICE',
          question: question.question || `Generated question ${index + 1}`,
          options: Array.isArray(question.options) ? question.options : [],
          correctAnswer: question.correctAnswer || '',
          explanation: question.explanation || '',
          difficulty: question.difficulty || validatedData.difficulty,
          points: question.points || 1,
        };
      });

      return NextResponse.json({
        success: true,
        data: {
          questions: validatedQuestions,
          metadata: {
            contentLength: validatedData.content.length,
            questionCount: validatedQuestions.length,
            difficulty: validatedData.difficulty,
            questionTypes: validatedData.questionTypes,
          },
        },
        message: 'Quiz questions generated successfully',
      });
    } catch (aiError) {
      console.error('AI generation error:', aiError);
      
      // Return fallback questions if AI fails
      const fallbackQuestions = generateFallbackQuestions(validatedData);
      
      return NextResponse.json({
        success: true,
        data: {
          questions: fallbackQuestions,
          metadata: {
            contentLength: validatedData.content.length,
            questionCount: fallbackQuestions.length,
            difficulty: validatedData.difficulty,
            questionTypes: validatedData.questionTypes,
            fallback: true,
          },
        },
        message: 'Quiz questions generated using fallback method',
      });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error generating quiz:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Fallback question generation when AI fails
function generateFallbackQuestions(data: z.infer<typeof generateQuizSchema>) {
  const fallbackQuestions = [];
  const contentWords = data.content.split(' ').filter(word => word.length > 3);
  
  for (let i = 0; i < Math.min(data.questionCount, 5); i++) {
    if (data.questionTypes.includes('MULTIPLE_CHOICE')) {
      fallbackQuestions.push({
        type: 'MULTIPLE_CHOICE',
        question: `What is the main topic discussed in the content?`,
        options: [
          'Option A - Based on content analysis',
          'Option B - Alternative interpretation',
          'Option C - Different perspective',
          'Option D - Contrasting viewpoint',
        ],
        correctAnswer: 'Option A - Based on content analysis',
        explanation: 'This question is generated from content analysis.',
        difficulty: data.difficulty,
        points: 1,
      });
    }
    
    if (data.questionTypes.includes('TRUE_FALSE') && fallbackQuestions.length < data.questionCount) {
      fallbackQuestions.push({
        type: 'TRUE_FALSE',
        question: `The content discusses important concepts related to ${data.subject || 'the topic'}.`,
        options: ['True', 'False'],
        correctAnswer: 'True',
        explanation: 'This statement is generally true based on the content provided.',
        difficulty: data.difficulty,
        points: 1,
      });
    }
  }
  
  return fallbackQuestions;
}
