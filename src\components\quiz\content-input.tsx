'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileText, Upload, Link as LinkIcon, Image, ArrowRight } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useDropzone } from 'react-dropzone';

interface ContentInputProps {
  method?: string | null;
  onSubmit: (data: any) => void;
}

export function ContentInput({ method, onSubmit }: ContentInputProps) {
  const [activeMethod, setActiveMethod] = useState(method || 'text');
  const [textContent, setTextContent] = useState('');
  const [urlContent, setUrlContent] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  const { getRootProps, getInputProps, isDragActive, acceptedFiles } = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'image/*': ['.png', '.jpg', '.jpeg'],
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  const handleTextSubmit = () => {
    if (!textContent.trim()) {
      toast({
        title: 'Content required',
        description: 'Please enter some content to generate questions from.',
        variant: 'destructive',
      });
      return;
    }

    onSubmit({
      content: textContent,
      metadata: {
        wordCount: textContent.split(' ').length,
        source: 'direct_input',
      },
    });
  };

  const handleUrlSubmit = async () => {
    if (!urlContent.trim()) {
      toast({
        title: 'URL required',
        description: 'Please enter a URL to extract content from.',
        variant: 'destructive',
      });
      return;
    }

    setIsProcessing(true);
    try {
      const response = await fetch('/api/ai/parse', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: urlContent,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to parse URL content');
      }

      const result = await response.json();
      onSubmit(result.data);
    } catch (error) {
      console.error('Error parsing URL:', error);
      toast({
        title: 'URL parsing failed',
        description: 'Failed to extract content from the URL. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFileSubmit = async () => {
    if (acceptedFiles.length === 0) {
      toast({
        title: 'File required',
        description: 'Please select a file to upload.',
        variant: 'destructive',
      });
      return;
    }

    const file = acceptedFiles[0];
    setIsProcessing(true);

    try {
      // Convert file to base64
      const fileData = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = () => {
          const base64 = (reader.result as string).split(',')[1];
          resolve(base64);
        };
        reader.readAsDataURL(file);
      });

      const response = await fetch('/api/ai/parse', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileData,
          fileName: file.name,
          mimeType: file.type,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to parse file content');
      }

      const result = await response.json();
      onSubmit(result.data);
    } catch (error) {
      console.error('Error parsing file:', error);
      toast({
        title: 'File parsing failed',
        description: 'Failed to extract content from the file. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const methods = [
    {
      id: 'text',
      title: 'Text Content',
      description: 'Paste or type your content directly',
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      id: 'upload',
      title: 'Upload File',
      description: 'PDF, DOCX, or text files',
      icon: Upload,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      id: 'url',
      title: 'From URL',
      description: 'Extract content from web pages',
      icon: LinkIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      id: 'image',
      title: 'From Image',
      description: 'Extract text from images (OCR)',
      icon: Image,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Choose Content Source
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {methods.map((method) => {
            const Icon = method.icon;
            return (
              <button
                key={method.id}
                onClick={() => setActiveMethod(method.id)}
                className={`p-4 rounded-lg border-2 transition-all ${
                  activeMethod === method.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className={`p-3 rounded-lg ${method.bgColor} mb-3 inline-block`}>
                  <Icon className={`h-6 w-6 ${method.color}`} />
                </div>
                <h3 className="font-medium text-gray-900 mb-1">{method.title}</h3>
                <p className="text-sm text-gray-600">{method.description}</p>
              </button>
            );
          })}
        </div>
      </div>

      <div className="border-t pt-6">
        {activeMethod === 'text' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Content
              </label>
              <textarea
                value={textContent}
                onChange={(e) => setTextContent(e.target.value)}
                placeholder="Paste your content here... (minimum 50 characters)"
                className="w-full h-64 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              />
              <p className="text-sm text-gray-600 mt-2">
                {textContent.length} characters, {textContent.split(' ').filter(w => w).length} words
              </p>
            </div>
            <Button onClick={handleTextSubmit} className="w-full">
              Continue with Text Content
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        )}

        {activeMethod === 'upload' && (
          <div className="space-y-4">
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <input {...getInputProps()} />
              <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              {isDragActive ? (
                <p className="text-blue-600">Drop the file here...</p>
              ) : (
                <div>
                  <p className="text-gray-600 mb-2">
                    Drag and drop a file here, or click to select
                  </p>
                  <p className="text-sm text-gray-500">
                    Supports PDF, DOCX, TXT, and image files (max 10MB)
                  </p>
                </div>
              )}
            </div>
            
            {acceptedFiles.length > 0 && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">Selected File:</h4>
                <p className="text-sm text-gray-600">
                  {acceptedFiles[0].name} ({Math.round(acceptedFiles[0].size / 1024)} KB)
                </p>
              </div>
            )}

            <Button
              onClick={handleFileSubmit}
              disabled={acceptedFiles.length === 0 || isProcessing}
              className="w-full"
            >
              {isProcessing ? 'Processing...' : 'Continue with File'}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        )}

        {activeMethod === 'url' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Website URL
              </label>
              <input
                type="url"
                value={urlContent}
                onChange={(e) => setUrlContent(e.target.value)}
                placeholder="https://example.com/article"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <p className="text-sm text-gray-600 mt-2">
                Enter a URL to extract content from web pages, articles, or documentation
              </p>
            </div>
            <Button
              onClick={handleUrlSubmit}
              disabled={!urlContent.trim() || isProcessing}
              className="w-full"
            >
              {isProcessing ? 'Extracting Content...' : 'Continue with URL'}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        )}

        {activeMethod === 'image' && (
          <div className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-sm text-yellow-800">
                <strong>Coming Soon:</strong> Image OCR functionality will be available in the next update.
                For now, please use text input or file upload methods.
              </p>
            </div>
            <Button disabled className="w-full">
              Image OCR (Coming Soon)
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
