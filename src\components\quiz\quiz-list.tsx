'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Search, 
  Eye, 
  Edit, 
  Share2, 
  Trash2, 
  MoreHorizontal,
  Clock,
  Users,
  Globe,
  Lock
} from 'lucide-react';
import Link from 'next/link';
import { formatRelativeTime } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface Quiz {
  id: string;
  title: string;
  description?: string | null;
  updatedAt: Date;
  isPublic: boolean;
  questions: any[];
  _count: {
    attempts: number;
  };
}

interface QuizListProps {
  quizzes: Quiz[];
  currentPage: number;
  totalPages: number;
  searchQuery: string;
}

export function QuizList({ quizzes, currentPage, totalPages, searchQuery }: QuizListProps) {
  const [search, setSearch] = useState(searchQuery);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const router = useRouter();
  const { toast } = useToast();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams();
    if (search) params.set('search', search);
    router.push(`/quiz?${params.toString()}`);
  };

  const handleDelete = async (quizId: string) => {
    if (!confirm('Are you sure you want to delete this quiz? This action cannot be undone.')) {
      return;
    }

    setIsDeleting(quizId);
    try {
      const response = await fetch(`/api/quiz/${quizId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast({
          title: 'Quiz deleted',
          description: 'The quiz has been successfully deleted.',
        });
        router.refresh();
      } else {
        throw new Error('Failed to delete quiz');
      }
    } catch (error) {
      console.error('Error deleting quiz:', error);
      toast({
        title: 'Delete failed',
        description: 'Failed to delete the quiz. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(null);
    }
  };

  const handleShare = async (quiz: Quiz) => {
    const shareUrl = `${window.location.origin}/quiz/${quiz.id}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: quiz.title,
          text: quiz.description || 'Check out this quiz!',
          url: shareUrl,
        });
      } catch (error) {
        navigator.clipboard.writeText(shareUrl);
        toast({
          title: 'Link copied!',
          description: 'Quiz link has been copied to your clipboard.',
        });
      }
    } else {
      navigator.clipboard.writeText(shareUrl);
      toast({
        title: 'Link copied!',
        description: 'Quiz link has been copied to your clipboard.',
      });
    }
  };

  if (quizzes.length === 0 && !searchQuery) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <svg
            className="mx-auto h-24 w-24"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">
          No quizzes yet
        </h3>
        <p className="text-gray-600 mb-6">
          Create your first quiz to get started with QuizCraft AI
        </p>
        <Link href="/quiz/create">
          <Button size="lg">
            Create Your First Quiz
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <form onSubmit={handleSearch} className="flex space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                placeholder="Search quizzes..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <Button type="submit">Search</Button>
            {searchQuery && (
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setSearch('');
                  router.push('/quiz');
                }}
              >
                Clear
              </Button>
            )}
          </form>
        </CardContent>
      </Card>

      {/* Results */}
      {searchQuery && (
        <div className="text-sm text-gray-600">
          {quizzes.length} result{quizzes.length !== 1 ? 's' : ''} for "{searchQuery}"
        </div>
      )}

      {/* Quiz Grid */}
      {quizzes.length === 0 ? (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No quizzes found
          </h3>
          <p className="text-gray-600">
            Try adjusting your search terms or create a new quiz
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {quizzes.map((quiz) => (
            <Card key={quiz.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg truncate">
                      {quiz.title}
                    </CardTitle>
                    {quiz.description && (
                      <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                        {quiz.description}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center space-x-1 ml-2">
                    {quiz.isPublic ? (
                      <Globe className="h-4 w-4 text-green-600" title="Public" />
                    ) : (
                      <Lock className="h-4 w-4 text-gray-400" title="Private" />
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{quiz.questions.length} questions</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4" />
                      <span>{quiz._count.attempts} attempts</span>
                    </div>
                  </div>
                </div>
                
                <div className="text-xs text-gray-500 mb-4">
                  Updated {formatRelativeTime(quiz.updatedAt)}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex space-x-1">
                    <Link href={`/quiz/${quiz.id}`}>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Link href={`/quiz/${quiz.id}/edit`}>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleShare(quiz)}
                    >
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(quiz.id)}
                    disabled={isDeleting === quiz.id}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <Button
            variant="outline"
            disabled={currentPage === 1}
            onClick={() => {
              const params = new URLSearchParams();
              if (searchQuery) params.set('search', searchQuery);
              params.set('page', (currentPage - 1).toString());
              router.push(`/quiz?${params.toString()}`);
            }}
          >
            Previous
          </Button>
          
          <div className="flex space-x-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = i + 1;
              return (
                <Button
                  key={page}
                  variant={currentPage === page ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => {
                    const params = new URLSearchParams();
                    if (searchQuery) params.set('search', searchQuery);
                    params.set('page', page.toString());
                    router.push(`/quiz?${params.toString()}`);
                  }}
                >
                  {page}
                </Button>
              );
            })}
          </div>
          
          <Button
            variant="outline"
            disabled={currentPage === totalPages}
            onClick={() => {
              const params = new URLSearchParams();
              if (searchQuery) params.set('search', searchQuery);
              params.set('page', (currentPage + 1).toString());
              router.push(`/quiz?${params.toString()}`);
            }}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}
