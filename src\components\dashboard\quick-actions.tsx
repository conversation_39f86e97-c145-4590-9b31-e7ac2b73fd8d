import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, FileText, Upload, Link as LinkIcon, Image } from 'lucide-react';
import Link from 'next/link';

export function QuickActions() {
  const actions = [
    {
      title: 'Create from Text',
      description: 'Paste or type content directly',
      icon: FileText,
      href: '/quiz/create?method=text',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Upload Document',
      description: 'PDF, DOCX, or text files',
      icon: Upload,
      href: '/quiz/create?method=upload',
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'From URL',
      description: 'Extract content from web pages',
      icon: LinkIcon,
      href: '/quiz/create?method=url',
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      title: 'From Image',
      description: 'Extract text from images',
      icon: Image,
      href: '/quiz/create?method=image',
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Plus className="h-5 w-5" />
          <span>Quick Actions</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {actions.map((action, index) => {
            const Icon = action.icon;
            return (
              <Link key={index} href={action.href}>
                <Button
                  variant="ghost"
                  className="w-full justify-start h-auto p-4 hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${action.bgColor}`}>
                      <Icon className={`h-4 w-4 ${action.color}`} />
                    </div>
                    <div className="text-left">
                      <div className="font-medium text-gray-900">
                        {action.title}
                      </div>
                      <div className="text-sm text-gray-600">
                        {action.description}
                      </div>
                    </div>
                  </div>
                </Button>
              </Link>
            );
          })}
        </div>

        <div className="mt-6 pt-4 border-t">
          <Link href="/templates">
            <Button variant="outline" className="w-full">
              Browse Templates
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
}
