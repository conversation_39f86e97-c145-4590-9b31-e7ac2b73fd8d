# 🧠 QuizCraftAI - AI-Powered Quiz Generation Platform

> Transform any content into engaging quizzes with the power of AI

[![Next.js](https://img.shields.io/badge/Next.js-14+-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind-3.0+-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)
[![OpenAI](https://img.shields.io/badge/OpenAI-GPT--4-412991?style=flat-square&logo=openai)](https://openai.com/)

## 🚀 Product Name Suggestions

Based on domain availability research, here are our top product name recommendations:

| Product Name | Domain | Status | Description |
|--------------|--------|--------|-------------|
| **QuizForge** | `quizforge.com` | ✅ Available | Craft quizzes like a master blacksmith |
| **QuizMind** | `quizmind.com` | ✅ Available | AI-powered intelligent quiz creation |
| **QuizLab** | `quizlab.com` | ✅ Available | Your laboratory for quiz experimentation |
| **QuizStudio** | `quizstudio.com` | ✅ Available | Professional quiz creation studio |
| **QuizCraft** | `quizcraft.com` | ✅ Available | Artisanal quiz crafting platform |

*Recommended: **QuizForge** - Strong, memorable, and conveys the idea of crafting/forging quizzes*

## 📋 Table of Contents

- [Features](#-features)
- [System Architecture](#-system-architecture)
- [User Workflow](#-user-workflow)
- [Tech Stack](#-tech-stack)
- [Project Structure](#-project-structure)
- [Installation](#-installation)
- [Environment Setup](#-environment-setup)
- [API Documentation](#-api-documentation)
- [Usage](#-usage)
- [Contributing](#-contributing)
- [License](#-license)

## ✨ Features

### Core Features
- 🤖 **AI-Powered Generation**: Leverage OpenAI GPT-4 for intelligent quiz creation
- 📄 **Multi-Format Input**: Support for text, PDF, DOCX, images, and URLs
- 🎯 **Question Variety**: MCQ, True/False, Fill-in-blank, and Short answer questions
- 📊 **Bloom's Taxonomy**: Difficulty levels based on educational standards
- 🎨 **Real-time Preview**: See your quiz as you build it
- 📱 **Responsive Design**: Works seamlessly on desktop and mobile

### Advanced Features
- 🔐 **Secure Authentication**: NextAuth.js with multiple OAuth providers
- 📈 **Analytics Dashboard**: Track quiz performance and engagement
- 🎨 **Template Library**: Pre-built quiz formats and themes
- 🔄 **Drag & Drop**: Intuitive file upload interface
- 💾 **Auto-save**: Never lose your work
- 🌐 **Share & Embed**: Easy quiz sharing and embedding options

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[Next.js 14 App Router]
        UI --> |TypeScript| COMP[React Components]
        UI --> |Styling| TW[Tailwind CSS]
        UI --> |Forms| RHF[React Hook Form]
        UI --> |State| ZUS[Zustand Store]
        UI --> |Server State| RQ[React Query]
    end

    subgraph "API Layer"
        API[Next.js API Routes]
        API --> AUTH[NextAuth.js]
        API --> UPLOAD[File Upload Handler]
        API --> PARSER[Content Parser]
        API --> QUIZ[Quiz Generator]
    end

    subgraph "AI Services"
        OPENAI[OpenAI GPT-4 API]
        PARSER --> OPENAI
        QUIZ --> OPENAI
    end

    subgraph "Database Layer"
        DB[(PostgreSQL)]
        PRISMA[Prisma ORM]
        API --> PRISMA
        PRISMA --> DB
    end

    subgraph "File Storage"
        STORAGE[File Storage Service]
        UPLOAD --> STORAGE
    end

    subgraph "Authentication"
        PROVIDERS[OAuth Providers]
        AUTH --> PROVIDERS
        AUTH --> DB
    end

    UI --> API
    
    classDef frontend fill:#e1f5fe
    classDef api fill:#f3e5f5
    classDef ai fill:#fff3e0
    classDef database fill:#e8f5e8
    classDef storage fill:#fce4ec
    classDef auth fill:#f1f8e9

    class UI,COMP,TW,RHF,ZUS,RQ frontend
    class API,AUTH,UPLOAD,PARSER,QUIZ api
    class OPENAI ai
    class DB,PRISMA database
    class STORAGE storage
    class PROVIDERS auth
```

## 👤 User Workflow

```mermaid
flowchart TD
    START([User Starts]) --> LOGIN{Authenticated?}
    LOGIN -->|No| AUTH[Sign In/Sign Up]
    LOGIN -->|Yes| DASHBOARD[Dashboard]
    AUTH --> DASHBOARD
    
    DASHBOARD --> CREATE[Create New Quiz]
    DASHBOARD --> MANAGE[Manage Existing Quizzes]
    DASHBOARD --> TEMPLATES[Browse Templates]
    
    CREATE --> INPUT{Choose Input Method}
    INPUT --> TEXT[Enter Text Content]
    INPUT --> UPLOAD[Upload Files]
    INPUT --> URL_INPUT[Enter URL]
    INPUT --> IMAGE[Upload Images]
    
    TEXT --> PROCESS[AI Content Processing]
    UPLOAD --> VALIDATE{File Validation}
    URL_INPUT --> SCRAPE[URL Content Scraping]
    IMAGE --> OCR[Image Text Extraction]
    
    VALIDATE -->|Valid| EXTRACT[Extract Content]
    VALIDATE -->|Invalid| ERROR[Show Error Message]
    ERROR --> INPUT
    
    EXTRACT --> PROCESS
    SCRAPE --> PROCESS
    OCR --> PROCESS
    
    PROCESS --> CONFIG[Configure Quiz Settings]
    CONFIG --> GENERATE[AI Quiz Generation]
    GENERATE --> PREVIEW[Real-time Preview]
    PREVIEW --> SAVE[Save Quiz]
    
    classDef startEnd fill:#4caf50,color:#fff
    classDef process fill:#2196f3,color:#fff
    classDef decision fill:#ff9800,color:#fff
    classDef ai fill:#9c27b0,color:#fff
    
    class START,DASHBOARD startEnd
    class TEXT,UPLOAD,URL_INPUT,IMAGE,EXTRACT,SCRAPE,OCR,CONFIG,PREVIEW,SAVE process
    class LOGIN,INPUT,VALIDATE decision
    class PROCESS,GENERATE ai
```

## 🛠️ Tech Stack

### Frontend
- **Framework**: Next.js 14+ with App Router
- **Language**: TypeScript 5.0+
- **Styling**: Tailwind CSS 3.0+
- **Forms**: React Hook Form
- **State Management**: Zustand
- **Server State**: React Query (TanStack Query)
- **UI Components**: Custom components with Headless UI

### Backend
- **Runtime**: Node.js with Next.js API Routes
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **File Upload**: UploadThing
- **AI Integration**: OpenAI API (GPT-4)

### DevOps & Tools
- **Package Manager**: pnpm
- **Linting**: ESLint + Prettier
- **Type Checking**: TypeScript
- **Database Migration**: Prisma Migrate
- **Deployment**: Vercel (recommended)

## 📁 Project Structure

```
quizcraft-ai/
├── README.md
├── package.json
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
├── prisma/
│   ├── schema.prisma
│   └── migrations/
├── src/
│   ├── app/
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   ├── globals.css
│   │   ├── api/
│   │   │   ├── auth/
│   │   │   ├── quiz/
│   │   │   ├── upload/
│   │   │   └── ai/
│   │   ├── dashboard/
│   │   ├── quiz/
│   │   └── auth/
│   ├── components/
│   │   ├── ui/
│   │   ├── forms/
│   │   ├── quiz/
│   │   └── layout/
│   ├── lib/
│   │   ├── auth.ts
│   │   ├── db.ts
│   │   ├── openai.ts
│   │   └── utils.ts
│   ├── hooks/
│   ├── store/
│   ├── types/
│   └── utils/
├── public/
└── docs/
```

## 🚀 Installation

### Prerequisites
- Node.js 18.0 or later
- PostgreSQL 14.0 or later
- pnpm (recommended) or npm

### Quick Start

1. **Clone the repository**
```bash
git clone https://github.com/HectorTa1989/quizcraft-ai.git
cd quizcraft-ai
```

2. **Install dependencies**
```bash
pnpm install
```

3. **Set up environment variables**
```bash
cp .env.example .env.local
```

4. **Configure your environment** (see Environment Setup below)

5. **Set up the database**
```bash
pnpm db:push
pnpm db:seed
```

6. **Start the development server**
```bash
pnpm dev
```

Visit `http://localhost:3000` to see your application running!

## 🔧 Environment Setup

Create a `.env.local` file in the root directory:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/quizcraft"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# OpenAI
OPENAI_API_KEY="your-openai-api-key"

# File Upload
UPLOADTHING_SECRET="your-uploadthing-secret"
UPLOADTHING_APP_ID="your-uploadthing-app-id"
```

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/signin` - Sign in user
- `POST /api/auth/signout` - Sign out user
- `GET /api/auth/session` - Get current session

### Quiz Management
- `GET /api/quiz` - Get user's quizzes
- `POST /api/quiz` - Create new quiz
- `GET /api/quiz/[id]` - Get specific quiz
- `PUT /api/quiz/[id]` - Update quiz
- `DELETE /api/quiz/[id]` - Delete quiz

### AI Generation
- `POST /api/ai/generate` - Generate quiz from content
- `POST /api/ai/parse` - Parse uploaded content

### File Upload
- `POST /api/upload` - Upload files
- `DELETE /api/upload/[id]` - Delete uploaded file

## 🎯 Usage

### Creating Your First Quiz

1. **Sign in** to your account
2. **Click "Create New Quiz"** from the dashboard
3. **Choose your input method**:
   - Upload a PDF or DOCX file
   - Paste text content
   - Enter a URL to scrape
   - Upload an image with text
4. **Configure quiz settings**:
   - Select difficulty level (Bloom's Taxonomy)
   - Choose question types
   - Set number of questions
5. **Generate and preview** your quiz
6. **Edit questions** if needed
7. **Save and share** your quiz

### Advanced Features

- **Templates**: Use pre-built quiz templates for common scenarios
- **Analytics**: Track quiz performance and user engagement
- **Collaboration**: Share quizzes with team members
- **Export**: Download quizzes in various formats

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Run tests: `pnpm test`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI for providing the GPT-4 API
- Vercel for the amazing Next.js framework
- The open-source community for the incredible tools and libraries

---

**Built with ❤️ by [HectorTa1989](https://github.com/HectorTa1989)**

For questions or support, please [open an issue](https://github.com/HectorTa1989/quizcraft-ai/issues) or contact <NAME_EMAIL>
